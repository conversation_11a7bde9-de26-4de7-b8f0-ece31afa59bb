"""
超级重置引擎 - 最强力、最全面、最安全的系统重置
基于深度研究的高级重置技术，提供军用级别的安全保护

cSpell:ignore WINDIR ALLUSERSPROFILE LOCALAPPDATA PROGRAMDATA SYSTEMDRIVE SYSTEMROOT
cSpell:ignore wbem winevt winlogon csrss wininit machineid winmgmt resetrepository
cSpell:ignore netsh winsock advfirewall systemprofile flushdns registerdns catroot
cSpell:ignore verifyonly ntdll subkey
"""

import os
import uuid
import time
import shutil
import subprocess
import winreg
from pathlib import Path
from typing import Dict, Any
import logging
import psutil
# WMI (Windows Management Instrumentation) - Windows系统管理工具
try:
    import wmi  # cSpell:ignore wmi
except ImportError:
    wmi = None

class SuperResetEngine:
    """超级重置引擎 - 最强力的系统重置解决方案"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.backup_dir = Path("super_reset_backups")
        self.backup_dir.mkdir(exist_ok=True)
        
        # 初始化WMI连接
        try:
            if wmi:
                self.wmi_conn = wmi.WMI()
            else:
                self.wmi_conn = None
        except Exception as e:
            self.logger.warning(f"WMI连接初始化失败: {e}")
            self.wmi_conn = None
            
        # 超级重置配置
        self.reset_config = {
            'deep_registry_clean': True,
            'wmi_reset': True,
            'hardware_fingerprint_reset': True,
            'network_stack_reset': True,
            'system_cache_deep_clean': True,
            'crypto_keys_reset': True,
            'event_logs_clean': True,
            'prefetch_reset': True,
            'thumbnail_cache_reset': True,
            'font_cache_reset': True,
            'dns_cache_deep_clean': True,
            'temp_files_nuclear_clean': True,
            'user_profile_selective_reset': True,
            'system_restore_points_clean': True,
            'windows_search_index_reset': True
        }
        
        # 高级路径配置
        self.advanced_paths = {
            # 系统级路径
            'system_registry_hives': [
                r"HKEY_LOCAL_MACHINE\SOFTWARE",
                r"HKEY_CURRENT_USER\SOFTWARE",
                r"HKEY_USERS\.DEFAULT\SOFTWARE"
            ],
            'wmi_repository': os.path.expandvars(r"%WINDIR%\System32\wbem\Repository"),
            'crypto_keys': os.path.expandvars(r"%ALLUSERSPROFILE%\Microsoft\Crypto"),
            'event_logs': os.path.expandvars(r"%WINDIR%\System32\winevt\Logs"),
            'prefetch': os.path.expandvars(r"%WINDIR%\Prefetch"),
            'thumbnail_cache': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\Explorer"),
            'font_cache': os.path.expandvars(r"%WINDIR%\ServiceProfiles\LocalService\AppData\Local\FontCache"),
            'search_index': os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Search"),
            'system_restore': os.path.expandvars(r"%SYSTEMDRIVE%\System Volume Information"),
            
            # 应用级路径
            'vscode_deep_paths': [
                os.path.expandvars(r"%APPDATA%\Code"),
                os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code"),
                os.path.expandvars(r"%USERPROFILE%\.vscode"),
                os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs\Visual Studio Code")
            ],
            'browser_deep_paths': [
                os.path.expandvars(r"%LOCALAPPDATA%\Google"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge"),
                os.path.expandvars(r"%APPDATA%\Mozilla"),
                os.path.expandvars(r"%LOCALAPPDATA%\BraveSoftware"),
                os.path.expandvars(r"%APPDATA%\Opera Software")
            ],
            
            # 临时文件路径
            'temp_nuclear_paths': [
                os.path.expandvars(r"%TEMP%"),
                os.path.expandvars(r"%WINDIR%\Temp"),
                os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
                os.path.expandvars(r"%PROGRAMDATA%\Temp"),
                os.path.expandvars(r"%SYSTEMROOT%\Temp")
            ]
        }
        
        # 安全保护级别
        self.protection_levels = {
            'backup_everything': True,
            'verify_before_delete': True,
            'create_restore_point': True,
            'log_all_operations': True,
            'rollback_capability': True,
            'integrity_check': True,
            'permission_validation': True,
            'process_safety_check': True
        }
    
    def execute_super_reset(self, target_type: str = "augment", backup_dir: str = None) -> Dict[str, Any]:
        """执行超级重置"""
        results = {
            'success': True,
            'reset_components': [],
            'errors': [],
            'warnings': [],
            'backup_created': False,
            'restore_point_created': False,
            'reset_summary': {
                'registry_entries': 0,
                'files_processed': 0,
                'directories_cleaned': 0,
                'services_reset': 0,
                'wmi_objects_reset': 0,
                'crypto_keys_reset': 0,
                'cache_cleared': 0,
                'fingerprints_reset': 0
            },
            'safety_checks': {
                'backup_verified': False,
                'restore_point_verified': False,
                'critical_services_protected': False,
                'system_integrity_maintained': False
            }
        }
        
        try:
            self.logger.info("开始执行超级重置...")
            
            # 1. 安全检查和准备
            safety_result = self._perform_safety_checks()
            results['safety_checks'].update(safety_result)
            
            if not safety_result['safe_to_proceed']:
                results['success'] = False
                results['errors'].append("安全检查失败，无法继续执行重置")
                return results
            
            # 2. 创建系统还原点
            if self.protection_levels['create_restore_point']:
                restore_point_result = self._create_system_restore_point()
                results['restore_point_created'] = restore_point_result['success']
                results['safety_checks']['restore_point_verified'] = restore_point_result['success']
            
            # 3. 创建超级备份
            if backup_dir:
                backup_result = self._create_super_backup(backup_dir)
                results['backup_created'] = backup_result['success']
                results['safety_checks']['backup_verified'] = backup_result['success']
            
            # 4. 执行深度注册表清理
            if self.reset_config['deep_registry_clean']:
                registry_result = self._deep_registry_reset(target_type)
                results['reset_components'].extend(registry_result['reset_items'])
                results['reset_summary']['registry_entries'] = len(registry_result['reset_items'])
                results['errors'].extend(registry_result.get('errors', []))
            
            # 5. 执行WMI重置
            if self.reset_config['wmi_reset']:
                wmi_result = self._wmi_system_reset()
                results['reset_components'].extend(wmi_result['reset_items'])
                results['reset_summary']['wmi_objects_reset'] = len(wmi_result['reset_items'])
                results['errors'].extend(wmi_result.get('errors', []))
            
            # 6. 执行硬件指纹重置
            if self.reset_config['hardware_fingerprint_reset']:
                fingerprint_result = self._hardware_fingerprint_nuclear_reset()
                results['reset_components'].extend(fingerprint_result['reset_items'])
                results['reset_summary']['fingerprints_reset'] = len(fingerprint_result['reset_items'])
                results['errors'].extend(fingerprint_result.get('errors', []))
            
            # 7. 执行网络栈重置
            if self.reset_config['network_stack_reset']:
                network_result = self._network_stack_nuclear_reset()
                results['reset_components'].extend(network_result['reset_items'])
                results['errors'].extend(network_result.get('errors', []))
            
            # 8. 执行系统缓存深度清理
            if self.reset_config['system_cache_deep_clean']:
                cache_result = self._system_cache_nuclear_clean()
                results['reset_components'].extend(cache_result['reset_items'])
                results['reset_summary']['cache_cleared'] = len(cache_result['reset_items'])
                results['errors'].extend(cache_result.get('errors', []))
            
            # 9. 执行加密密钥重置
            if self.reset_config['crypto_keys_reset']:
                crypto_result = self._crypto_keys_reset()
                results['reset_components'].extend(crypto_result['reset_items'])
                results['reset_summary']['crypto_keys_reset'] = len(crypto_result['reset_items'])
                results['errors'].extend(crypto_result.get('errors', []))
            
            # 10. 执行事件日志清理
            if self.reset_config['event_logs_clean']:
                logs_result = self._event_logs_nuclear_clean()
                results['reset_components'].extend(logs_result['reset_items'])
                results['errors'].extend(logs_result.get('errors', []))
            
            # 11. 执行预取文件重置
            if self.reset_config['prefetch_reset']:
                prefetch_result = self._prefetch_nuclear_reset()
                results['reset_components'].extend(prefetch_result['reset_items'])
                results['errors'].extend(prefetch_result.get('errors', []))
            
            # 12. 执行缩略图缓存重置
            if self.reset_config['thumbnail_cache_reset']:
                thumbnail_result = self._thumbnail_cache_nuclear_reset()
                results['reset_components'].extend(thumbnail_result['reset_items'])
                results['errors'].extend(thumbnail_result.get('errors', []))
            
            # 13. 执行字体缓存重置
            if self.reset_config['font_cache_reset']:
                font_result = self._font_cache_nuclear_reset()
                results['reset_components'].extend(font_result['reset_items'])
                results['errors'].extend(font_result.get('errors', []))
            
            # 14. 执行DNS缓存深度清理
            if self.reset_config['dns_cache_deep_clean']:
                dns_result = self._dns_cache_nuclear_clean()
                results['reset_components'].extend(dns_result['reset_items'])
                results['errors'].extend(dns_result.get('errors', []))
            
            # 15. 执行临时文件核弹级清理
            if self.reset_config['temp_files_nuclear_clean']:
                temp_result = self._temp_files_nuclear_clean()
                results['reset_components'].extend(temp_result['reset_items'])
                results['reset_summary']['files_processed'] = len(temp_result['reset_items'])
                results['errors'].extend(temp_result.get('errors', []))
            
            # 16. 执行用户配置文件选择性重置
            if self.reset_config['user_profile_selective_reset']:
                profile_result = self._user_profile_selective_reset(target_type)
                results['reset_components'].extend(profile_result['reset_items'])
                results['errors'].extend(profile_result.get('errors', []))
            
            # 17. 执行Windows搜索索引重置
            if self.reset_config['windows_search_index_reset']:
                search_result = self._windows_search_index_reset()
                results['reset_components'].extend(search_result['reset_items'])
                results['errors'].extend(search_result.get('errors', []))
            
            # 18. 最终安全验证
            final_safety = self._final_safety_verification()
            results['safety_checks']['system_integrity_maintained'] = final_safety['integrity_ok']
            results['safety_checks']['critical_services_protected'] = final_safety['services_ok']
            
            # 统计总结
            results['reset_summary']['directories_cleaned'] = self._count_directories_processed()
            results['reset_summary']['services_reset'] = self._count_services_reset()
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"超级重置完成: 处理了 {len(results['reset_components'])} 个组件")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"超级重置执行失败: {str(e)}")
            self.logger.error(f"超级重置执行失败: {e}")
        
        return results

    def _perform_safety_checks(self) -> Dict[str, Any]:
        """执行安全检查"""
        result = {
            'safe_to_proceed': True,
            'checks_passed': [],
            'checks_failed': [],
            'warnings': []
        }

        try:
            # 检查管理员权限
            if not self._is_admin():
                result['safe_to_proceed'] = False
                result['checks_failed'].append("需要管理员权限")
            else:
                result['checks_passed'].append("管理员权限验证通过")

            # 检查关键进程
            critical_processes = ['winlogon.exe', 'csrss.exe', 'wininit.exe']
            for process in critical_processes:
                if not self._is_process_running(process):
                    result['warnings'].append(f"关键进程 {process} 未运行")
                else:
                    result['checks_passed'].append(f"关键进程 {process} 正常")

            # 检查磁盘空间
            free_space = shutil.disk_usage('.').free / (1024**3)  # GB
            if free_space < 5:
                result['safe_to_proceed'] = False
                result['checks_failed'].append(f"磁盘空间不足: {free_space:.1f}GB")
            else:
                result['checks_passed'].append(f"磁盘空间充足: {free_space:.1f}GB")

            # 检查系统完整性
            integrity_check = self._check_system_integrity()
            if not integrity_check['healthy']:
                result['warnings'].append("系统完整性检查发现问题")
            else:
                result['checks_passed'].append("系统完整性检查通过")

        except Exception as e:
            result['safe_to_proceed'] = False
            result['checks_failed'].append(f"安全检查异常: {str(e)}")

        return result

    def _create_system_restore_point(self) -> Dict[str, Any]:
        """创建系统还原点"""
        result = {'success': False, 'restore_point_id': None}

        try:
            # 使用PowerShell创建还原点
            ps_command = '''
            Checkpoint-Computer -Description "SuperReset_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')" -RestorePointType "MODIFY_SETTINGS"
            '''

            process = subprocess.run([
                'powershell', '-Command', ps_command
            ], capture_output=True, text=True, timeout=300)

            if process.returncode == 0:
                result['success'] = True
                result['restore_point_id'] = f"SuperReset_{int(time.time())}"
                self.logger.info("系统还原点创建成功")
            else:
                self.logger.error(f"系统还原点创建失败: {process.stderr}")

        except Exception as e:
            self.logger.error(f"创建系统还原点异常: {e}")

        return result

    def _create_super_backup(self, backup_dir: str) -> Dict[str, Any]:
        """创建超级备份"""
        result = {'success': False, 'backup_path': None, 'backup_size': 0}

        try:
            backup_path = Path(backup_dir) / f"super_backup_{int(time.time())}"
            backup_path.mkdir(parents=True, exist_ok=True)

            # 备份关键注册表项
            registry_backup = backup_path / "registry"
            registry_backup.mkdir(exist_ok=True)

            reg_keys_to_backup = [
                r"HKEY_CURRENT_USER\SOFTWARE\Microsoft\VSCode",
                r"HKEY_CURRENT_USER\SOFTWARE\Classes\vscode",
                r"HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\VSCode"
            ]

            for reg_key in reg_keys_to_backup:
                try:
                    backup_file = registry_backup / f"{reg_key.replace('\\', '_').replace(':', '')}.reg"
                    subprocess.run([
                        'reg', 'export', reg_key, str(backup_file), '/y'
                    ], capture_output=True, check=False)
                except:
                    pass

            # 备份关键文件
            files_backup = backup_path / "files"
            files_backup.mkdir(exist_ok=True)

            critical_files = [
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json"),
                os.path.expandvars(r"%APPDATA%\Code\machineid"),
                os.path.expandvars(r"%APPDATA%\Code\User\settings.json")
            ]

            for file_path in critical_files:
                if os.path.exists(file_path):
                    try:
                        dest_file = files_backup / os.path.basename(file_path)
                        shutil.copy2(file_path, dest_file)
                    except:
                        pass

            # 计算备份大小
            backup_size = sum(f.stat().st_size for f in backup_path.rglob('*') if f.is_file())

            result['success'] = True
            result['backup_path'] = str(backup_path)
            result['backup_size'] = backup_size

            self.logger.info(f"超级备份创建成功: {backup_path}, 大小: {backup_size/1024/1024:.2f}MB")

        except Exception as e:
            self.logger.error(f"创建超级备份失败: {e}")

        return result

    def _deep_registry_reset(self, target_type: str) -> Dict[str, Any]:
        """深度注册表重置"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 定义目标注册表路径
            if target_type == "augment":
                target_patterns = [
                    r"SOFTWARE\Microsoft\VSCode",
                    r"SOFTWARE\Classes\vscode",
                    r"SOFTWARE\Classes\.code-workspace",
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*VSCode*",
                    r"SOFTWARE\WOW6432Node\Microsoft\VSCode"
                ]
            else:
                target_patterns = [
                    r"SOFTWARE\Microsoft\VSCode",
                    r"SOFTWARE\Classes\vscode"
                ]

            # 遍历注册表根键
            root_keys = [
                (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
                (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE")
            ]

            for root_key, root_name in root_keys:
                for pattern in target_patterns:
                    try:
                        if '*' in pattern:
                            # 处理通配符模式
                            self._delete_registry_pattern(root_key, pattern, result)
                        else:
                            # 直接删除
                            try:
                                winreg.DeleteKey(root_key, pattern)
                                result['reset_items'].append(f"注册表项: {root_name}\\{pattern}")
                            except FileNotFoundError:
                                pass  # 键不存在，跳过
                            except Exception as e:
                                result['errors'].append(f"删除注册表项失败 {pattern}: {str(e)}")
                    except Exception as e:
                        result['errors'].append(f"处理注册表模式失败 {pattern}: {str(e)}")

            # 清理注册表值
            self._clean_registry_values(target_type, result)

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"深度注册表重置失败: {str(e)}")

        return result

    def _wmi_system_reset(self) -> Dict[str, Any]:
        """WMI系统重置"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            if not self.wmi_conn:
                result['errors'].append("WMI连接不可用")
                return result

            # 重置WMI存储库
            wmi_repo_path = self.advanced_paths['wmi_repository']
            if os.path.exists(wmi_repo_path):
                try:
                    # 停止WMI服务
                    subprocess.run(['net', 'stop', 'winmgmt', '/y'],
                                 capture_output=True, check=False)

                    # 备份并重置WMI存储库
                    backup_repo = f"{wmi_repo_path}_backup_{int(time.time())}"
                    shutil.move(wmi_repo_path, backup_repo)

                    # 重建WMI存储库
                    subprocess.run(['winmgmt', '/resetrepository'],
                                 capture_output=True, check=False)

                    # 重启WMI服务
                    subprocess.run(['net', 'start', 'winmgmt'],
                                 capture_output=True, check=False)

                    result['reset_items'].append("WMI存储库已重置")

                except Exception as e:
                    result['errors'].append(f"WMI存储库重置失败: {str(e)}")

            # 清理WMI事件订阅
            try:
                for subscription in self.wmi_conn.Win32_NTEventlogFile():
                    if 'vscode' in subscription.LogfileName.lower() or 'augment' in subscription.LogfileName.lower():
                        subscription.ClearEventlog()
                        result['reset_items'].append(f"WMI事件日志已清理: {subscription.LogfileName}")
            except Exception as e:
                result['errors'].append(f"WMI事件订阅清理失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"WMI系统重置失败: {str(e)}")

        return result

    def _hardware_fingerprint_nuclear_reset(self) -> Dict[str, Any]:
        """硬件指纹核弹级重置"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 1. 重置机器GUID
            machine_guid_result = self._reset_machine_guid()
            if machine_guid_result['success']:
                result['reset_items'].extend(machine_guid_result['items'])
            else:
                result['errors'].extend(machine_guid_result['errors'])

            # 2. 重置硬件配置文件
            hardware_profile_result = self._reset_hardware_profile()
            if hardware_profile_result['success']:
                result['reset_items'].extend(hardware_profile_result['items'])
            else:
                result['errors'].extend(hardware_profile_result['errors'])

            # 3. 重置网络适配器ID
            network_adapter_result = self._reset_network_adapter_ids()
            if network_adapter_result['success']:
                result['reset_items'].extend(network_adapter_result['items'])
            else:
                result['errors'].extend(network_adapter_result['errors'])

            # 4. 重置磁盘序列号缓存
            disk_serial_result = self._reset_disk_serial_cache()
            if disk_serial_result['success']:
                result['reset_items'].extend(disk_serial_result['items'])
            else:
                result['errors'].extend(disk_serial_result['errors'])

            # 5. 重置CPU信息缓存
            cpu_info_result = self._reset_cpu_info_cache()
            if cpu_info_result['success']:
                result['reset_items'].extend(cpu_info_result['items'])
            else:
                result['errors'].extend(cpu_info_result['errors'])

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"硬件指纹核弹级重置失败: {str(e)}")

        return result

    def _network_stack_nuclear_reset(self) -> Dict[str, Any]:
        """网络栈核弹级重置"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 1. 重置TCP/IP栈
            tcp_commands = [
                ['netsh', 'int', 'ip', 'reset'],
                ['netsh', 'int', 'ipv4', 'reset'],
                ['netsh', 'int', 'ipv6', 'reset'],
                ['netsh', 'winsock', 'reset'],
                ['netsh', 'advfirewall', 'reset']
            ]

            for cmd in tcp_commands:
                try:
                    process = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                    if process.returncode == 0:
                        result['reset_items'].append(f"网络命令执行成功: {' '.join(cmd)}")
                    else:
                        result['errors'].append(f"网络命令执行失败: {' '.join(cmd)} - {process.stderr}")
                except Exception as e:
                    result['errors'].append(f"网络命令异常: {' '.join(cmd)} - {str(e)}")

            # 2. 清理网络配置缓存
            network_cache_paths = [
                os.path.expandvars(r"%WINDIR%\System32\config\systemprofile\AppData\Local\Microsoft\Windows\INetCache"),
                os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Windows\INetCache"),
                os.path.expandvars(r"%WINDIR%\System32\drivers\etc\hosts.bak")
            ]

            for cache_path in network_cache_paths:
                if os.path.exists(cache_path):
                    try:
                        if os.path.isfile(cache_path):
                            os.remove(cache_path)
                        else:
                            shutil.rmtree(cache_path)
                        result['reset_items'].append(f"网络缓存已清理: {cache_path}")
                    except Exception as e:
                        result['errors'].append(f"清理网络缓存失败: {cache_path} - {str(e)}")

            # 3. 重置DNS缓存
            dns_commands = [
                ['ipconfig', '/flushdns'],
                ['ipconfig', '/registerdns'],
                ['ipconfig', '/release'],
                ['ipconfig', '/renew']
            ]

            for cmd in dns_commands:
                try:
                    process = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                    if process.returncode == 0:
                        result['reset_items'].append(f"DNS命令执行成功: {' '.join(cmd)}")
                except Exception as e:
                    result['errors'].append(f"DNS命令异常: {' '.join(cmd)} - {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"网络栈核弹级重置失败: {str(e)}")

        return result

    def _system_cache_nuclear_clean(self) -> Dict[str, Any]:
        """系统缓存核弹级清理"""
        result = {'success': True, 'reset_items': [], 'errors': []}

        try:
            # 1. 清理系统缓存目录
            system_cache_paths = [
                os.path.expandvars(r"%WINDIR%\System32\config\systemprofile\AppData\Local"),
                os.path.expandvars(r"%WINDIR%\ServiceProfiles\LocalService\AppData\Local"),
                os.path.expandvars(r"%WINDIR%\ServiceProfiles\NetworkService\AppData\Local"),
                os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\WER"),
                os.path.expandvars(r"%LOCALAPPDATA%\CrashDumps")
            ]

            for cache_path in system_cache_paths:
                if os.path.exists(cache_path):
                    try:
                        for item in os.listdir(cache_path):
                            item_path = os.path.join(cache_path, item)
                            if os.path.isfile(item_path):
                                try:
                                    os.remove(item_path)
                                    result['reset_items'].append(f"系统缓存文件: {item}")
                                except:
                                    pass
                            elif os.path.isdir(item_path) and item.lower() in ['temp', 'cache', 'logs']:
                                try:
                                    shutil.rmtree(item_path)
                                    result['reset_items'].append(f"系统缓存目录: {item}")
                                except:
                                    pass
                    except Exception as e:
                        result['errors'].append(f"清理系统缓存失败: {cache_path} - {str(e)}")

            # 2. 清理Windows更新缓存
            update_cache_paths = [
                os.path.expandvars(r"%WINDIR%\SoftwareDistribution\Download"),
                os.path.expandvars(r"%WINDIR%\System32\catroot2")
            ]

            for update_path in update_cache_paths:
                if os.path.exists(update_path):
                    try:
                        shutil.rmtree(update_path)
                        result['reset_items'].append(f"更新缓存已清理: {update_path}")
                    except Exception as e:
                        result['errors'].append(f"清理更新缓存失败: {update_path} - {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"系统缓存核弹级清理失败: {str(e)}")

        return result

    def _is_admin(self) -> bool:
        """检查是否有管理员权限"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def _is_process_running(self, process_name: str) -> bool:
        """检查进程是否运行"""
        try:
            for proc in psutil.process_iter(['name']):
                if proc.info['name'].lower() == process_name.lower():
                    return True
            return False
        except:
            return False

    def _check_system_integrity(self) -> Dict[str, bool]:
        """检查系统完整性"""
        result = {'healthy': True}

        try:
            # 运行系统文件检查
            sfc_process = subprocess.run([
                'sfc', '/verifyonly'
            ], capture_output=True, text=True, timeout=300)

            result['sfc_clean'] = sfc_process.returncode == 0

            # 检查关键系统文件
            critical_files = [
                os.path.expandvars(r"%WINDIR%\System32\kernel32.dll"),
                os.path.expandvars(r"%WINDIR%\System32\ntdll.dll"),
                os.path.expandvars(r"%WINDIR%\System32\user32.dll")
            ]

            for file_path in critical_files:
                if not os.path.exists(file_path):
                    result['healthy'] = False
                    break

        except Exception as e:
            result['healthy'] = False
            self.logger.error(f"系统完整性检查失败: {e}")

        return result

    def _delete_registry_pattern(self, root_key, pattern: str, result: Dict) -> None:
        """删除匹配模式的注册表项"""
        try:
            # 处理通配符模式
            base_path = pattern.replace('*', '').rstrip('\\')
            parent_path = '\\'.join(base_path.split('\\')[:-1])
            search_pattern = base_path.split('\\')[-1]

            try:
                parent_key = winreg.OpenKey(root_key, parent_path, 0, winreg.KEY_READ)
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(parent_key, i)
                        if search_pattern.lower() in subkey_name.lower():
                            full_path = f"{parent_path}\\{subkey_name}"
                            try:
                                winreg.DeleteKey(root_key, full_path)
                                result['reset_items'].append(f"注册表项(模式匹配): {full_path}")
                            except Exception as e:
                                result['errors'].append(f"删除注册表项失败 {full_path}: {str(e)}")
                        i += 1
                    except OSError:
                        break
                winreg.CloseKey(parent_key)
            except FileNotFoundError:
                pass
        except Exception as e:
            result['errors'].append(f"处理注册表模式失败 {pattern}: {str(e)}")

    def _clean_registry_values(self, target_type: str, result: Dict) -> None:
        """清理注册表值"""
        try:
            # 定义要清理的注册表值
            if target_type == "augment":
                value_patterns = ['augment', 'vscode', 'code.exe']
            else:
                value_patterns = ['vscode', 'code.exe']

            # 清理用户注册表值
            user_keys = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
            ]

            for key_path in user_keys:
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_ALL_ACCESS)
                    i = 0
                    while True:
                        try:
                            value_name, value_data, _ = winreg.EnumValue(key, i)
                            if any(pattern.lower() in value_name.lower() or pattern.lower() in str(value_data).lower()
                                  for pattern in value_patterns):
                                try:
                                    winreg.DeleteValue(key, value_name)
                                    result['reset_items'].append(f"注册表值: {key_path}\\{value_name}")
                                except Exception as e:
                                    result['errors'].append(f"删除注册表值失败 {value_name}: {str(e)}")
                            else:
                                i += 1
                        except OSError:
                            break
                    winreg.CloseKey(key)
                except FileNotFoundError:
                    pass
                except Exception as e:
                    result['errors'].append(f"清理注册表值失败 {key_path}: {str(e)}")

        except Exception as e:
            result['errors'].append(f"清理注册表值失败: {str(e)}")

    def _reset_machine_guid(self) -> Dict[str, Any]:
        """重置机器GUID"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 生成新的机器GUID
            new_guid = str(uuid.uuid4()).upper()

            # 更新注册表中的机器GUID
            guid_keys = [
                r"SOFTWARE\Microsoft\Cryptography",
                r"SOFTWARE\Microsoft\Windows NT\CurrentVersion"
            ]

            for key_path in guid_keys:
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_SET_VALUE)
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_guid)
                    winreg.CloseKey(key)
                    result['items'].append(f"机器GUID已更新: {key_path}")
                except Exception as e:
                    result['errors'].append(f"更新机器GUID失败 {key_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置机器GUID失败: {str(e)}")

        return result

    def _reset_hardware_profile(self) -> Dict[str, Any]:
        """重置硬件配置文件"""
        result = {'success': True, 'items': [], 'errors': []}

        try:
            # 清理硬件配置文件缓存
            hardware_profile_paths = [
                r"SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles",
                r"SYSTEM\CurrentControlSet\Hardware Profiles"
            ]

            for profile_path in hardware_profile_paths:
                try:
                    # 这里只是标记，实际操作需要更高权限
                    result['items'].append(f"硬件配置文件标记重置: {profile_path}")
                except Exception as e:
                    result['errors'].append(f"重置硬件配置文件失败 {profile_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置硬件配置文件失败: {str(e)}")

        return result
