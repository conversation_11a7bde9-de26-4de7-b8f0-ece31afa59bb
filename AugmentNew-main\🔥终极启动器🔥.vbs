' 🔥 AugmentNew 终极启动器 🔥
' 一键激活所有功能，智能选择AI助手，完美启动保证
' 解决所有启动问题，提供最佳用户体验

Option Explicit

Dim objShell, objFSO
Dim strCurrentDir, strPythonPath

' 初始化
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
strCurrentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 主启动流程
Main()

Sub Main()
    On Error Resume Next
    
    ' 显示欢迎界面
    ShowWelcomeScreen()
    
    ' 快速启动检查
    If QuickStartCheck() Then
        LaunchProgram()
    Else
        ' 详细设置模式
        DetailedSetup()
    End If
    
End Sub

Sub ShowWelcomeScreen()
    Dim strWelcome, intChoice
    
    strWelcome = "🔥 AugmentNew 终极启动器 🔥" & vbCrLf & vbCrLf & _
                 "🚀 一键激活所有功能" & vbCrLf & _
                 "🤖 智能AI助手选择" & vbCrLf & _
                 "🛡️ 完美安全保障" & vbCrLf & _
                 "⚡ 极速启动体验" & vbCrLf & vbCrLf & _
                 "选择启动模式:" & vbCrLf & vbCrLf & _
                 "1. 🚀 极速启动 (推荐)" & vbCrLf & _
                 "2. 🔧 详细设置" & vbCrLf & _
                 "3. 🤖 AI助手选择" & vbCrLf & _
                 "4. 🛡️ 安全模式" & vbCrLf & _
                 "5. 🚨 紧急修复"
    
    intChoice = InputBox(strWelcome, "AugmentNew 终极启动器", "1")
    
    Select Case intChoice
        Case "1"
            ' 极速启动
            QuickLaunch()
        Case "2"
            ' 详细设置
            DetailedSetup()
        Case "3"
            ' AI助手选择
            AIAssistantSelection()
        Case "4"
            ' 安全模式
            SafeMode()
        Case "5"
            ' 紧急修复
            EmergencyRepair()
        Case Else
            ' 默认极速启动
            QuickLaunch()
    End Select
End Sub

Sub QuickLaunch()
    On Error Resume Next
    
    MsgBox "🚀 极速启动模式" & vbCrLf & vbCrLf & _
           "正在快速检查环境并启动程序...", vbInformation, "极速启动"
    
    ' 快速环境检查
    If Not QuickEnvironmentCheck() Then
        MsgBox "❌ 环境检查失败，切换到详细设置模式", vbExclamation
        DetailedSetup()
        Exit Sub
    End If
    
    ' 直接启动
    LaunchProgram()
End Sub

Sub AIAssistantSelection()
    On Error Resume Next
    
    Dim strAIList, strSelected, arrAI, i
    
    ' AI助手列表 (可折叠显示)
    arrAI = Array( _
        "Augment Code - VSCode AI助手", _
        "Cursor AI - 智能编程IDE", _
        "GitHub Copilot - 代码补全", _
        "Tabnine - AI代码助手", _
        "Codeium - 免费AI编程", _
        "Claude AI - Anthropic AI", _
        "CodeWhisperer - Amazon AI", _
        "Sourcegraph Cody - 企业AI" _
    )
    
    ' 创建选择界面
    strAIList = "🤖 选择要重置的AI助手" & vbCrLf & vbCrLf & _
                "📋 支持的AI助手列表:" & vbCrLf & vbCrLf
    
    For i = 0 To UBound(arrAI)
        strAIList = strAIList & (i + 1) & ". " & arrAI(i) & vbCrLf
    Next
    
    strAIList = strAIList & vbCrLf & "🎯 选择方式:" & vbCrLf & _
                "• 输入数字选择单个 (如: 1)" & vbCrLf & _
                "• 输入多个数字选择多个 (如: 1,2,3)" & vbCrLf & _
                "• 输入 'all' 选择全部" & vbCrLf & _
                "• 直接回车使用默认设置"
    
    strSelected = InputBox(strAIList, "AI助手选择", "all")
    
    ' 保存选择并启动
    SaveAISelection(strSelected)
    LaunchProgram()
End Sub

Sub SaveAISelection(strSelection)
    On Error Resume Next
    
    ' 创建配置文件
    Dim strConfigFile, strConfig
    strConfigFile = strCurrentDir & "\ai_selection.txt"
    
    strConfig = "# AugmentNew AI助手选择配置" & vbCrLf & _
                "# 生成时间: " & Now() & vbCrLf & _
                "# 选择: " & strSelection & vbCrLf & vbCrLf
    
    If strSelection = "all" Or strSelection = "" Then
        strConfig = strConfig & "selected_ai=all" & vbCrLf
    Else
        strConfig = strConfig & "selected_ai=" & strSelection & vbCrLf
    End If
    
    ' 写入文件
    Dim objFile
    Set objFile = objFSO.CreateTextFile(strConfigFile, True)
    objFile.Write strConfig
    objFile.Close
    
    MsgBox "✅ AI助手选择已保存" & vbCrLf & vbCrLf & _
           "选择: " & strSelection, vbInformation
End Sub

Sub SafeMode()
    On Error Resume Next
    
    MsgBox "🛡️ 安全模式启动" & vbCrLf & vbCrLf & _
           "将执行完整的安全检查和保护措施...", vbInformation, "安全模式"
    
    ' 创建系统还原点
    CreateRestorePoint()
    
    ' 备份关键文件
    BackupCriticalFiles()
    
    ' 安全启动
    LaunchProgram()
End Sub

Sub EmergencyRepair()
    On Error Resume Next
    
    Dim intRepairChoice
    
    intRepairChoice = MsgBox("🚨 紧急修复模式" & vbCrLf & vbCrLf & _
                            "检测到可能的系统问题" & vbCrLf & vbCrLf & _
                            "是否执行自动修复？", vbYesNo + vbExclamation, "紧急修复")
    
    If intRepairChoice = vbYes Then
        ' 执行修复
        PerformEmergencyRepair()
    End If
    
    ' 修复后启动
    LaunchProgram()
End Sub

Function QuickStartCheck()
    On Error Resume Next
    QuickStartCheck = True
    
    ' 检查Python
    objShell.Run "python --version", 0, True
    If Err.Number <> 0 Then
        QuickStartCheck = False
        Exit Function
    End If
    
    ' 检查主文件
    If Not objFSO.FileExists(strCurrentDir & "\main.py") Then
        QuickStartCheck = False
        Exit Function
    End If
    
    ' 检查关键目录
    If Not objFSO.FolderExists(strCurrentDir & "\gui") Then
        QuickStartCheck = False
        Exit Function
    End If
    
End Function

Function QuickEnvironmentCheck()
    On Error Resume Next
    QuickEnvironmentCheck = True
    
    ' 快速检查磁盘空间
    Dim objDrive
    Set objDrive = objFSO.GetDrive("C:")
    If objDrive.FreeSpace < 536870912 Then ' 小于512MB
        QuickEnvironmentCheck = False
        Exit Function
    End If
    
    ' 检查Python环境
    strPythonPath = DetectPython()
    If strPythonPath = "" Then
        QuickEnvironmentCheck = False
        Exit Function
    End If
    
End Function

Function DetectPython()
    On Error Resume Next
    DetectPython = ""
    
    ' 简化的Python检测
    Dim arrPaths, strPath
    arrPaths = Array("python", "python3", "py")
    
    For Each strPath In arrPaths
        objShell.Run strPath & " --version", 0, True
        If Err.Number = 0 Then
            DetectPython = strPath
            Exit Function
        End If
        Err.Clear
    Next
End Function

Sub DetailedSetup()
    On Error Resume Next
    
    MsgBox "🔧 详细设置模式" & vbCrLf & vbCrLf & _
           "正在执行完整的环境检查和配置...", vbInformation, "详细设置"
    
    ' 1. 环境检查
    If Not FullEnvironmentCheck() Then
        MsgBox "❌ 环境检查失败，请手动解决问题", vbCritical
        Exit Sub
    End If
    
    ' 2. 依赖安装
    InstallDependencies()
    
    ' 3. 配置优化
    OptimizeConfiguration()
    
    ' 4. 启动程序
    LaunchProgram()
End Sub

Function FullEnvironmentCheck()
    On Error Resume Next
    FullEnvironmentCheck = True
    
    ' 详细环境检查
    Dim strReport
    strReport = "🔍 环境检查报告" & vbCrLf & vbCrLf
    
    ' Python检查
    strPythonPath = DetectPython()
    If strPythonPath <> "" Then
        strReport = strReport & "✅ Python环境: " & strPythonPath & vbCrLf
    Else
        strReport = strReport & "❌ Python环境: 未找到" & vbCrLf
        FullEnvironmentCheck = False
    End If
    
    ' 文件检查
    If objFSO.FileExists(strCurrentDir & "\main.py") Then
        strReport = strReport & "✅ 主程序: 存在" & vbCrLf
    Else
        strReport = strReport & "❌ 主程序: 缺失" & vbCrLf
        FullEnvironmentCheck = False
    End If
    
    ' 目录检查
    If objFSO.FolderExists(strCurrentDir & "\gui") Then
        strReport = strReport & "✅ GUI模块: 存在" & vbCrLf
    Else
        strReport = strReport & "❌ GUI模块: 缺失" & vbCrLf
        FullEnvironmentCheck = False
    End If
    
    ' 显示报告
    MsgBox strReport, vbInformation, "环境检查"
    
End Function

Sub InstallDependencies()
    On Error Resume Next
    
    MsgBox "📦 正在安装依赖包..." & vbCrLf & vbCrLf & _
           "这可能需要几分钟时间", vbInformation, "依赖安装"
    
    ' 安装关键包
    Dim arrPackages, strPackage
    arrPackages = Array("customtkinter", "Pillow", "requests")
    
    For Each strPackage In arrPackages
        objShell.Run strPythonPath & " -m pip install " & strPackage, 0, True
    Next
    
    MsgBox "✅ 依赖安装完成", vbInformation
End Sub

Sub OptimizeConfiguration()
    On Error Resume Next
    
    ' 创建优化配置
    Dim strConfigFile, strConfig
    strConfigFile = strCurrentDir & "\launch_config.json"
    
    strConfig = "{" & vbCrLf & _
                "  ""launch_mode"": ""optimized""," & vbCrLf & _
                "  ""ai_assistants"": ""all""," & vbCrLf & _
                "  ""safety_mode"": true," & vbCrLf & _
                "  ""auto_backup"": true," & vbCrLf & _
                "  ""timestamp"": """ & Now() & """" & vbCrLf & _
                "}"
    
    Dim objFile
    Set objFile = objFSO.CreateTextFile(strConfigFile, True)
    objFile.Write strConfig
    objFile.Close
End Sub

Sub CreateRestorePoint()
    On Error Resume Next
    
    ' 创建系统还原点
    objShell.Run "powershell -Command ""Checkpoint-Computer -Description 'AugmentNew_Launch' -RestorePointType 'MODIFY_SETTINGS'""", 0, True
End Sub

Sub BackupCriticalFiles()
    On Error Resume Next
    
    ' 创建备份目录
    Dim strBackupDir
    strBackupDir = strCurrentDir & "\launch_backup_" & Year(Now()) & Month(Now()) & Day(Now())
    
    If Not objFSO.FolderExists(strBackupDir) Then
        objFSO.CreateFolder(strBackupDir)
    End If
    
    ' 备份关键文件
    Dim arrFiles, strFile
    arrFiles = Array("main.py", "super_config.json", "version.txt")
    
    For Each strFile In arrFiles
        If objFSO.FileExists(strCurrentDir & "\" & strFile) Then
            objFSO.CopyFile strCurrentDir & "\" & strFile, strBackupDir & "\" & strFile
        End If
    Next
End Sub

Sub PerformEmergencyRepair()
    On Error Resume Next
    
    MsgBox "🔧 正在执行紧急修复...", vbInformation, "紧急修复"
    
    ' 重新创建main.py (如果缺失)
    If Not objFSO.FileExists(strCurrentDir & "\main.py") Then
        CreateMainPy()
    End If
    
    ' 重新创建必要目录
    Dim arrDirs, strDir
    arrDirs = Array("gui", "utils", "augutils", "logs", "backups")
    
    For Each strDir In arrDirs
        If Not objFSO.FolderExists(strCurrentDir & "\" & strDir) Then
            objFSO.CreateFolder(strCurrentDir & "\" & strDir)
        End If
    Next
    
    MsgBox "✅ 紧急修复完成", vbInformation
End Sub

Sub CreateMainPy()
    On Error Resume Next
    
    ' 创建简化的main.py
    Dim strMainPy
    strMainPy = "#!/usr/bin/env python3" & vbCrLf & _
                "# -*- coding: utf-8 -*-" & vbCrLf & _
                "import sys" & vbCrLf & _
                "import os" & vbCrLf & _
                "try:" & vbCrLf & _
                "    from gui.main_window import MainWindow" & vbCrLf & _
                "    app = MainWindow()" & vbCrLf & _
                "    app.run()" & vbCrLf & _
                "except Exception as e:" & vbCrLf & _
                "    print(f'启动失败: {e}')" & vbCrLf & _
                "    input('按回车键退出...')" & vbCrLf
    
    Dim objFile
    Set objFile = objFSO.CreateTextFile(strCurrentDir & "\main.py", True)
    objFile.Write strMainPy
    objFile.Close
End Sub

Sub LaunchProgram()
    On Error Resume Next
    
    MsgBox "🚀 启动AugmentNew..." & vbCrLf & vbCrLf & _
           "程序即将启动，请稍候...", vbInformation, "启动程序"
    
    ' 切换到程序目录
    objShell.CurrentDirectory = strCurrentDir
    
    ' 启动程序
    Dim strLaunchCmd
    If strPythonPath = "" Then strPythonPath = "python"
    strLaunchCmd = """" & strPythonPath & """ """ & strCurrentDir & "\main.py"""
    
    objShell.Run strLaunchCmd, 1, False
    
    ' 等待启动
    WScript.Sleep 3000
    
    ' 成功提示
    MsgBox "🎉 AugmentNew 启动成功！" & vbCrLf & vbCrLf & _
           "🔥 终极启动器功能:" & vbCrLf & _
           "✅ 一键激活所有功能" & vbCrLf & _
           "✅ 智能AI助手选择" & vbCrLf & _
           "✅ 完美安全保障" & vbCrLf & _
           "✅ 极速启动体验" & vbCrLf & vbCrLf & _
           "现在可以享受强大的AI助手重置功能！", _
           vbInformation + vbOKOnly, "启动成功"
End Sub

' 清理对象
Set objShell = Nothing
Set objFSO = Nothing
