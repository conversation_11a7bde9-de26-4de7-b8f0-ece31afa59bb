#!/usr/bin/env python3
"""
多浏览器清理模块
支持Chrome、Edge、Firefox等主流浏览器的深度清理
"""

import os
import shutil
import json
import sqlite3
import winreg
from pathlib import Path
from typing import List, Dict, Optional
import logging

class BrowserCleaner:
    """多浏览器清理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.user_profile = os.environ.get('USERPROFILE', '')
        self.appdata_local = os.path.join(self.user_profile, 'AppData', 'Local')
        self.appdata_roaming = os.path.join(self.user_profile, 'AppData', 'Roaming')
        
    def get_browser_paths(self) -> Dict[str, Dict[str, str]]:
        """获取各浏览器的路径配置"""
        return {
            'chrome': {
                'name': 'Google Chrome',
                'user_data': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Cache'),
                'cookies': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Cookies'),
                'local_storage': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Local Storage'),
                'session_storage': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Session Storage'),
                'preferences': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Preferences'),
                'web_data': os.path.join(self.appdata_local, 'Google', 'Chrome', 'User Data', 'Default', 'Web Data'),
            },
            'edge': {
                'name': 'Microsoft Edge',
                'user_data': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Cache'),
                'cookies': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Cookies'),
                'local_storage': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Local Storage'),
                'session_storage': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Session Storage'),
                'preferences': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Preferences'),
                'web_data': os.path.join(self.appdata_local, 'Microsoft', 'Edge', 'User Data', 'Default', 'Web Data'),
            },
            'firefox': {
                'name': 'Mozilla Firefox',
                'profiles': os.path.join(self.appdata_roaming, 'Mozilla', 'Firefox', 'Profiles'),
                'cache': os.path.join(self.appdata_local, 'Mozilla', 'Firefox', 'Profiles'),
            },
            'opera': {
                'name': 'Opera',
                'user_data': os.path.join(self.appdata_roaming, 'Opera Software', 'Opera Stable'),
                'cache': os.path.join(self.appdata_local, 'Opera Software', 'Opera Stable', 'Cache'),
            },
            'brave': {
                'name': 'Brave Browser',
                'user_data': os.path.join(self.appdata_local, 'BraveSoftware', 'Brave-Browser', 'User Data'),
                'cache': os.path.join(self.appdata_local, 'BraveSoftware', 'Brave-Browser', 'User Data', 'Default', 'Cache'),
            }
        }
    
    def detect_installed_browsers(self) -> List[str]:
        """检测已安装的浏览器"""
        installed = []
        browser_paths = self.get_browser_paths()
        
        for browser_key, paths in browser_paths.items():
            if browser_key == 'firefox':
                if os.path.exists(paths['profiles']):
                    installed.append(browser_key)
            else:
                if os.path.exists(paths['user_data']):
                    installed.append(browser_key)
        
        return installed
    
    def backup_browser_data(self, browser: str, backup_dir: str) -> bool:
        """备份浏览器数据"""
        try:
            browser_paths = self.get_browser_paths()
            if browser not in browser_paths:
                return False
            
            browser_backup_dir = os.path.join(backup_dir, f"{browser}_backup")
            os.makedirs(browser_backup_dir, exist_ok=True)
            
            paths = browser_paths[browser]
            
            # 备份主要数据目录
            if 'user_data' in paths and os.path.exists(paths['user_data']):
                backup_path = os.path.join(browser_backup_dir, 'user_data')
                shutil.copytree(paths['user_data'], backup_path, ignore_errors=True)
            
            # 备份Firefox配置文件
            if browser == 'firefox' and os.path.exists(paths['profiles']):
                backup_path = os.path.join(browser_backup_dir, 'profiles')
                shutil.copytree(paths['profiles'], backup_path, ignore_errors=True)
            
            self.logger.info(f"已备份 {browser} 数据到 {browser_backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"备份 {browser} 数据失败: {e}")
            return False
    
    def clear_browser_augment_data(self, browser: str) -> Dict[str, any]:
        """清理指定浏览器的Augment相关数据"""
        result = {
            'success': False,
            'browser': browser,
            'cleared_items': [],
            'errors': []
        }
        
        try:
            browser_paths = self.get_browser_paths()
            if browser not in browser_paths:
                result['errors'].append(f"不支持的浏览器: {browser}")
                return result
            
            paths = browser_paths[browser]
            
            # 清理Local Storage中的Augment数据
            if 'local_storage' in paths:
                self._clear_local_storage_augment(paths['local_storage'], result)
            
            # 清理Cookies中的Augment相关数据
            if 'cookies' in paths:
                self._clear_cookies_augment(paths['cookies'], result)
            
            # 清理缓存
            if 'cache' in paths:
                self._clear_cache(paths['cache'], result)
            
            # 清理Session Storage
            if 'session_storage' in paths:
                self._clear_session_storage(paths['session_storage'], result)
            
            # 清理Web Data (表单数据等)
            if 'web_data' in paths:
                self._clear_web_data_augment(paths['web_data'], result)
            
            # 修改Preferences文件
            if 'preferences' in paths:
                self._modify_preferences_augment(paths['preferences'], result)
            
            # Firefox特殊处理
            if browser == 'firefox':
                self._clear_firefox_augment_data(paths, result)
            
            result['success'] = len(result['errors']) == 0
            
        except Exception as e:
            result['errors'].append(f"清理 {browser} 失败: {str(e)}")
            self.logger.error(f"清理 {browser} 失败: {e}")
        
        return result
    
    def clear_all_browsers_augment_data(self, backup_dir: str = None) -> Dict[str, any]:
        """清理所有浏览器的Augment数据"""
        results = {
            'success': True,
            'browsers_processed': [],
            'total_cleared': 0,
            'errors': []
        }
        
        installed_browsers = self.detect_installed_browsers()
        
        for browser in installed_browsers:
            try:
                # 备份数据
                if backup_dir:
                    self.backup_browser_data(browser, backup_dir)
                
                # 清理数据
                browser_result = self.clear_browser_augment_data(browser)
                results['browsers_processed'].append(browser_result)
                
                if browser_result['success']:
                    results['total_cleared'] += len(browser_result['cleared_items'])
                else:
                    results['success'] = False
                    results['errors'].extend(browser_result['errors'])
                    
            except Exception as e:
                results['success'] = False
                results['errors'].append(f"处理 {browser} 时出错: {str(e)}")
        
        return results

    def _clear_local_storage_augment(self, local_storage_path: str, result: Dict) -> None:
        """清理Local Storage中的Augment数据"""
        try:
            if not os.path.exists(local_storage_path):
                return

            augment_patterns = [
                'augment', 'vscode-augment', 'augmentcode',
                'github.com_augment', 'app.augmentcode.com'
            ]

            for file_name in os.listdir(local_storage_path):
                file_path = os.path.join(local_storage_path, file_name)

                # 检查文件名是否包含Augment相关关键词
                if any(pattern in file_name.lower() for pattern in augment_patterns):
                    try:
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            result['cleared_items'].append(f"Local Storage: {file_name}")
                        elif os.path.isdir(file_path):
                            shutil.rmtree(file_path)
                            result['cleared_items'].append(f"Local Storage Dir: {file_name}")
                    except Exception as e:
                        result['errors'].append(f"删除Local Storage文件失败 {file_name}: {e}")

        except Exception as e:
            result['errors'].append(f"清理Local Storage失败: {e}")

    def _clear_cookies_augment(self, cookies_path: str, result: Dict) -> None:
        """清理Cookies中的Augment相关数据"""
        try:
            if not os.path.exists(cookies_path):
                return

            # 连接到Cookies数据库
            conn = sqlite3.connect(cookies_path)
            cursor = conn.cursor()

            # 查找Augment相关的cookies
            augment_domains = [
                '%augment%', '%augmentcode%', '%github.com%',
                '%vscode%', '%microsoft%'
            ]

            deleted_count = 0
            for domain in augment_domains:
                cursor.execute("DELETE FROM cookies WHERE host_key LIKE ?", (domain,))
                deleted_count += cursor.rowcount

            conn.commit()
            conn.close()

            if deleted_count > 0:
                result['cleared_items'].append(f"Cookies: 删除了 {deleted_count} 个相关cookie")

        except Exception as e:
            result['errors'].append(f"清理Cookies失败: {e}")

    def _clear_cache(self, cache_path: str, result: Dict) -> None:
        """清理浏览器缓存"""
        try:
            if not os.path.exists(cache_path):
                return

            # 删除缓存目录中的所有文件
            for root, dirs, files in os.walk(cache_path):
                for file in files:
                    try:
                        os.remove(os.path.join(root, file))
                    except:
                        pass
                for dir in dirs:
                    try:
                        shutil.rmtree(os.path.join(root, dir))
                    except:
                        pass

            result['cleared_items'].append("Cache: 已清理浏览器缓存")

        except Exception as e:
            result['errors'].append(f"清理缓存失败: {e}")

    def _clear_session_storage(self, session_storage_path: str, result: Dict) -> None:
        """清理Session Storage"""
        try:
            if not os.path.exists(session_storage_path):
                return

            # 删除所有session storage文件
            for file_name in os.listdir(session_storage_path):
                file_path = os.path.join(session_storage_path, file_name)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                except:
                    pass

            result['cleared_items'].append("Session Storage: 已清理")

        except Exception as e:
            result['errors'].append(f"清理Session Storage失败: {e}")

    def _clear_web_data_augment(self, web_data_path: str, result: Dict) -> None:
        """清理Web Data中的Augment相关数据"""
        try:
            if not os.path.exists(web_data_path):
                return

            conn = sqlite3.connect(web_data_path)
            cursor = conn.cursor()

            # 清理自动填充数据
            try:
                cursor.execute("DELETE FROM autofill WHERE name LIKE '%augment%'")
                cursor.execute("DELETE FROM autofill_profiles WHERE company_name LIKE '%augment%'")
                result['cleared_items'].append("Web Data: 已清理自动填充数据")
            except:
                pass

            conn.commit()
            conn.close()

        except Exception as e:
            result['errors'].append(f"清理Web Data失败: {e}")

    def _modify_preferences_augment(self, preferences_path: str, result: Dict) -> None:
        """修改Preferences文件，移除Augment相关配置"""
        try:
            if not os.path.exists(preferences_path):
                return

            with open(preferences_path, 'r', encoding='utf-8') as f:
                prefs = json.load(f)

            # 移除扩展相关配置
            extensions_to_remove = []
            if 'extensions' in prefs:
                if 'settings' in prefs['extensions']:
                    for ext_id, ext_data in prefs['extensions']['settings'].items():
                        if 'augment' in str(ext_data).lower():
                            extensions_to_remove.append(ext_id)

            for ext_id in extensions_to_remove:
                if ext_id in prefs['extensions']['settings']:
                    del prefs['extensions']['settings'][ext_id]
                    result['cleared_items'].append(f"Preferences: 移除扩展 {ext_id}")

            # 保存修改后的preferences
            with open(preferences_path, 'w', encoding='utf-8') as f:
                json.dump(prefs, f, indent=2)

        except Exception as e:
            result['errors'].append(f"修改Preferences失败: {e}")

    def _clear_firefox_augment_data(self, paths: Dict, result: Dict) -> None:
        """清理Firefox的Augment相关数据"""
        try:
            profiles_dir = paths.get('profiles')
            if not profiles_dir or not os.path.exists(profiles_dir):
                return

            # 遍历所有Firefox配置文件
            for profile_name in os.listdir(profiles_dir):
                profile_path = os.path.join(profiles_dir, profile_name)
                if not os.path.isdir(profile_path):
                    continue

                # 清理places.sqlite (历史记录和书签)
                places_db = os.path.join(profile_path, 'places.sqlite')
                if os.path.exists(places_db):
                    self._clear_firefox_places_augment(places_db, result)

                # 清理cookies.sqlite
                cookies_db = os.path.join(profile_path, 'cookies.sqlite')
                if os.path.exists(cookies_db):
                    self._clear_firefox_cookies_augment(cookies_db, result)

                # 清理webappsstore.sqlite (localStorage)
                webapps_db = os.path.join(profile_path, 'webappsstore.sqlite')
                if os.path.exists(webapps_db):
                    self._clear_firefox_webapps_augment(webapps_db, result)

        except Exception as e:
            result['errors'].append(f"清理Firefox数据失败: {e}")

    def _clear_firefox_places_augment(self, places_db_path: str, result: Dict) -> None:
        """清理Firefox places数据库中的Augment相关记录"""
        try:
            conn = sqlite3.connect(places_db_path)
            cursor = conn.cursor()

            # 删除Augment相关的历史记录
            cursor.execute("DELETE FROM moz_places WHERE url LIKE '%augment%'")
            cursor.execute("DELETE FROM moz_places WHERE url LIKE '%github.com%'")

            # 删除相关书签
            cursor.execute("DELETE FROM moz_bookmarks WHERE title LIKE '%augment%'")

            conn.commit()
            conn.close()

            result['cleared_items'].append("Firefox: 已清理历史记录和书签")

        except Exception as e:
            result['errors'].append(f"清理Firefox places失败: {e}")

    def _clear_firefox_cookies_augment(self, cookies_db_path: str, result: Dict) -> None:
        """清理Firefox cookies数据库"""
        try:
            conn = sqlite3.connect(cookies_db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM moz_cookies WHERE host LIKE '%augment%'")
            cursor.execute("DELETE FROM moz_cookies WHERE host LIKE '%github.com%'")

            conn.commit()
            conn.close()

            result['cleared_items'].append("Firefox: 已清理cookies")

        except Exception as e:
            result['errors'].append(f"清理Firefox cookies失败: {e}")

    def _clear_firefox_webapps_augment(self, webapps_db_path: str, result: Dict) -> None:
        """清理Firefox localStorage数据"""
        try:
            conn = sqlite3.connect(webapps_db_path)
            cursor = conn.cursor()

            cursor.execute("DELETE FROM webappsstore2 WHERE originKey LIKE '%augment%'")
            cursor.execute("DELETE FROM webappsstore2 WHERE originKey LIKE '%github%'")

            conn.commit()
            conn.close()

            result['cleared_items'].append("Firefox: 已清理localStorage")

        except Exception as e:
            result['errors'].append(f"清理Firefox localStorage失败: {e}")
