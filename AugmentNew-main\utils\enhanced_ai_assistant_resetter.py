"""
增强AI助手重置器 - 基于最新网络研究的全面解决方案
支持多种AI编程助手：Augment Code, Cursor AI, GitHub Copilot等
基于2024年最新的绕过技术和社区发现
"""

import os
import sys
import json
import uuid
import time
import shutil
import hashlib
import secrets
import subprocess
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging
import sqlite3

class EnhancedAIAssistantResetter:
    """增强AI助手重置器 - 支持多种AI编程助手"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的AI助手配置
        self.ai_assistants = {
            'augment_code': {
                'name': 'Augment Code',
                'extension_ids': ['augmentcode.augment'],
                'storage_keys': [
                    'augment.trial.status',
                    'augment.usage.count', 
                    'augment.trial.expiry',
                    'augment.device.id',
                    'augment.session.id'
                ],
                'registry_paths': [
                    r"SOFTWARE\Microsoft\VSCode\augment",
                    r"SOFTWARE\Classes\vscode\augment"
                ]
            },
            'cursor_ai': {
                'name': 'Cursor AI',
                'extension_ids': ['cursor.cursor'],
                'storage_keys': [
                    'cursor.trial.status',
                    'cursor.usage.count',
                    'cursor.device.fingerprint',
                    'cursor.machine.id',
                    'cursor.session.token'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage\storage.json"),
                    os.path.expandvars(r"%APPDATA%\Cursor\machine-id"),
                    os.path.expandvars(r"%LOCALAPPDATA%\Cursor\User Data\Default\Preferences")
                ]
            },
            'github_copilot': {
                'name': 'GitHub Copilot',
                'extension_ids': ['github.copilot', 'github.copilot-chat'],
                'storage_keys': [
                    'github.copilot.trial',
                    'github.copilot.usage',
                    'github.copilot.device',
                    'copilot.telemetry'
                ],
                'auth_files': [
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\github.copilot"),
                    os.path.expandvars(r"%APPDATA%\GitHub CLI\hosts.yml")
                ]
            },
            'tabnine': {
                'name': 'Tabnine',
                'extension_ids': ['tabnine.tabnine-vscode'],
                'storage_keys': [
                    'tabnine.trial.status',
                    'tabnine.device.id',
                    'tabnine.usage.stats'
                ],
                'config_files': [
                    os.path.expandvars(r"%APPDATA%\TabNine"),
                    os.path.expandvars(r"%LOCALAPPDATA%\TabNine")
                ]
            },
            'codeium': {
                'name': 'Codeium',
                'extension_ids': ['codeium.codeium'],
                'storage_keys': [
                    'codeium.trial.status',
                    'codeium.device.fingerprint',
                    'codeium.auth.token'
                ]
            }
        }
        
        # 基于网络研究的高级重置技术
        self.advanced_techniques = {
            'machine_fingerprint_reset': True,
            'telemetry_modification': True,
            'device_id_regeneration': True,
            'network_adapter_reset': True,
            'hardware_profile_modification': True,
            'browser_fingerprint_reset': True,
            'system_clock_manipulation': True,
            'virtual_machine_detection_bypass': True,
            'anti_detection_measures': True
        }
        
        # 检测到的绕过技术（基于网络研究）
        self.bypass_methods = {
            'cursor_machine_limit': {
                'description': '解决Cursor "Too many free trial accounts used on this machine" 问题',
                'methods': [
                    'machine_id_modification',
                    'device_fingerprint_reset', 
                    'network_adapter_mac_change',
                    'system_uuid_modification'
                ]
            },
            'trial_period_extension': {
                'description': '延长试用期限',
                'methods': [
                    'system_clock_rollback',
                    'trial_timestamp_modification',
                    'usage_counter_reset'
                ]
            },
            'suspicious_activity_bypass': {
                'description': '绕过"suspicious activity detected"检测',
                'methods': [
                    'request_pattern_randomization',
                    'user_agent_rotation',
                    'network_proxy_rotation'
                ]
            }
        }
    
    def reset_all_ai_assistants(self, backup_dir: str = None) -> Dict[str, Any]:
        """重置所有AI助手的试用状态"""
        results = {
            'success': True,
            'assistants_reset': [],
            'errors': [],
            'techniques_applied': [],
            'backup_created': False
        }
        
        try:
            self.logger.info("开始重置所有AI助手...")
            
            # 创建备份
            if backup_dir:
                backup_result = self._create_comprehensive_backup(backup_dir)
                results['backup_created'] = backup_result['success']
            
            # 重置每个AI助手
            for assistant_id, config in self.ai_assistants.items():
                try:
                    assistant_result = self._reset_single_assistant(assistant_id, config)
                    if assistant_result['success']:
                        results['assistants_reset'].append(config['name'])
                        results['techniques_applied'].extend(assistant_result['techniques'])
                    else:
                        results['errors'].extend(assistant_result['errors'])
                except Exception as e:
                    results['errors'].append(f"{config['name']} 重置失败: {str(e)}")
            
            # 应用高级绕过技术
            advanced_result = self._apply_advanced_bypass_techniques()
            results['techniques_applied'].extend(advanced_result['techniques'])
            results['errors'].extend(advanced_result['errors'])
            
            # 应用反检测措施
            anti_detection_result = self._apply_anti_detection_measures()
            results['techniques_applied'].extend(anti_detection_result['techniques'])
            
            if results['errors']:
                results['success'] = False
            
            self.logger.info(f"AI助手重置完成: {len(results['assistants_reset'])} 个助手已重置")
            
        except Exception as e:
            results['success'] = False
            results['errors'].append(f"AI助手重置失败: {str(e)}")
            self.logger.error(f"AI助手重置失败: {e}")
        
        return results
    
    def _reset_single_assistant(self, assistant_id: str, config: Dict) -> Dict[str, Any]:
        """重置单个AI助手"""
        result = {
            'success': True,
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 清理扩展存储数据
            if 'extension_ids' in config:
                for ext_id in config['extension_ids']:
                    storage_result = self._clean_extension_storage(ext_id)
                    if storage_result['success']:
                        result['techniques'].append(f"{config['name']} 扩展存储清理")
                    else:
                        result['errors'].extend(storage_result['errors'])
            
            # 2. 清理存储键
            if 'storage_keys' in config:
                keys_result = self._clean_storage_keys(config['storage_keys'])
                if keys_result['success']:
                    result['techniques'].append(f"{config['name']} 存储键清理")
                else:
                    result['errors'].extend(keys_result['errors'])
            
            # 3. 清理配置文件
            if 'config_files' in config:
                files_result = self._clean_config_files(config['config_files'])
                if files_result['success']:
                    result['techniques'].append(f"{config['name']} 配置文件清理")
                else:
                    result['errors'].extend(files_result['errors'])
            
            # 4. 清理注册表
            if 'registry_paths' in config:
                registry_result = self._clean_registry_paths(config['registry_paths'])
                if registry_result['success']:
                    result['techniques'].append(f"{config['name']} 注册表清理")
                else:
                    result['errors'].extend(registry_result['errors'])
            
            # 5. 清理认证文件
            if 'auth_files' in config:
                auth_result = self._clean_auth_files(config['auth_files'])
                if auth_result['success']:
                    result['techniques'].append(f"{config['name']} 认证文件清理")
                else:
                    result['errors'].extend(auth_result['errors'])
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置 {config['name']} 失败: {str(e)}")
        
        return result
    
    def _apply_advanced_bypass_techniques(self) -> Dict[str, Any]:
        """应用高级绕过技术"""
        result = {
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 机器指纹重置
            if self.advanced_techniques['machine_fingerprint_reset']:
                fingerprint_result = self._reset_machine_fingerprint()
                if fingerprint_result['success']:
                    result['techniques'].append("机器指纹重置")
                else:
                    result['errors'].extend(fingerprint_result['errors'])
            
            # 2. 遥测数据修改
            if self.advanced_techniques['telemetry_modification']:
                telemetry_result = self._modify_telemetry_data()
                if telemetry_result['success']:
                    result['techniques'].append("遥测数据修改")
                else:
                    result['errors'].extend(telemetry_result['errors'])
            
            # 3. 设备ID重新生成
            if self.advanced_techniques['device_id_regeneration']:
                device_result = self._regenerate_device_ids()
                if device_result['success']:
                    result['techniques'].append("设备ID重新生成")
                else:
                    result['errors'].extend(device_result['errors'])
            
            # 4. 网络适配器重置
            if self.advanced_techniques['network_adapter_reset']:
                network_result = self._reset_network_adapters()
                if network_result['success']:
                    result['techniques'].append("网络适配器重置")
                else:
                    result['errors'].extend(network_result['errors'])
            
            # 5. 硬件配置文件修改
            if self.advanced_techniques['hardware_profile_modification']:
                hardware_result = self._modify_hardware_profile()
                if hardware_result['success']:
                    result['techniques'].append("硬件配置文件修改")
                else:
                    result['errors'].extend(hardware_result['errors'])
            
        except Exception as e:
            result['errors'].append(f"应用高级绕过技术失败: {str(e)}")
        
        return result

    def _clean_extension_storage(self, extension_id: str) -> Dict[str, Any]:
        """清理扩展存储数据"""
        result = {'success': True, 'errors': []}

        try:
            # VSCode扩展存储路径
            storage_paths = [
                os.path.expandvars(rf"%APPDATA%\Code\User\globalStorage\{extension_id}"),
                os.path.expandvars(rf"%APPDATA%\Code\User\workspaceStorage\*\{extension_id}"),
                os.path.expandvars(rf"%LOCALAPPDATA%\Programs\Microsoft VS Code\resources\app\extensions\{extension_id}")
            ]

            for storage_path in storage_paths:
                if '*' in storage_path:
                    # 处理通配符路径
                    import glob
                    for path in glob.glob(storage_path):
                        if os.path.exists(path):
                            try:
                                shutil.rmtree(path)
                            except Exception as e:
                                result['errors'].append(f"删除扩展存储失败 {path}: {str(e)}")
                else:
                    if os.path.exists(storage_path):
                        try:
                            if os.path.isfile(storage_path):
                                os.remove(storage_path)
                            else:
                                shutil.rmtree(storage_path)
                        except Exception as e:
                            result['errors'].append(f"删除扩展存储失败 {storage_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理扩展存储失败: {str(e)}")

        return result

    def _clean_storage_keys(self, storage_keys: List[str]) -> Dict[str, Any]:
        """清理存储键"""
        result = {'success': True, 'errors': []}

        try:
            # VSCode storage.json文件
            storage_json_path = os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\storage.json")

            if os.path.exists(storage_json_path):
                try:
                    with open(storage_json_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)

                    # 删除指定的键
                    keys_removed = 0
                    for key in storage_keys:
                        if key in storage_data:
                            del storage_data[key]
                            keys_removed += 1

                        # 也检查包含该键的其他键
                        keys_to_remove = []
                        for existing_key in storage_data.keys():
                            if key.lower() in existing_key.lower():
                                keys_to_remove.append(existing_key)

                        for key_to_remove in keys_to_remove:
                            del storage_data[key_to_remove]
                            keys_removed += 1

                    # 写回文件
                    if keys_removed > 0:
                        with open(storage_json_path, 'w', encoding='utf-8') as f:
                            json.dump(storage_data, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"处理storage.json失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理存储键失败: {str(e)}")

        return result

    def _clean_config_files(self, config_files: List[str]) -> Dict[str, Any]:
        """清理配置文件"""
        result = {'success': True, 'errors': []}

        try:
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        if os.path.isfile(config_file):
                            # 如果是JSON文件，尝试清理内容而不是删除
                            if config_file.endswith('.json'):
                                self._clean_json_config(config_file)
                            else:
                                os.remove(config_file)
                        else:
                            shutil.rmtree(config_file)
                    except Exception as e:
                        result['errors'].append(f"清理配置文件失败 {config_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理配置文件失败: {str(e)}")

        return result

    def _clean_registry_paths(self, registry_paths: List[str]) -> Dict[str, Any]:
        """清理注册表路径"""
        result = {'success': True, 'errors': []}

        try:
            for reg_path in registry_paths:
                try:
                    # 尝试删除HKEY_CURRENT_USER下的路径
                    try:
                        winreg.DeleteKey(winreg.HKEY_CURRENT_USER, reg_path)
                    except FileNotFoundError:
                        pass  # 键不存在，跳过

                    # 尝试删除HKEY_LOCAL_MACHINE下的路径
                    try:
                        winreg.DeleteKey(winreg.HKEY_LOCAL_MACHINE, reg_path)
                    except FileNotFoundError:
                        pass  # 键不存在，跳过

                except Exception as e:
                    result['errors'].append(f"清理注册表路径失败 {reg_path}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理注册表路径失败: {str(e)}")

        return result

    def _clean_auth_files(self, auth_files: List[str]) -> Dict[str, Any]:
        """清理认证文件"""
        result = {'success': True, 'errors': []}

        try:
            for auth_file in auth_files:
                if os.path.exists(auth_file):
                    try:
                        if os.path.isfile(auth_file):
                            os.remove(auth_file)
                        else:
                            shutil.rmtree(auth_file)
                    except Exception as e:
                        result['errors'].append(f"清理认证文件失败 {auth_file}: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"清理认证文件失败: {str(e)}")

        return result

    def _reset_machine_fingerprint(self) -> Dict[str, Any]:
        """重置机器指纹"""
        result = {'success': True, 'errors': []}

        try:
            # 1. 重置机器ID
            machine_id_path = os.path.expandvars(r"%APPDATA%\Code\machineid")
            if os.path.exists(machine_id_path):
                try:
                    new_machine_id = secrets.token_hex(32)
                    with open(machine_id_path, 'w', encoding='utf-8') as f:
                        f.write(new_machine_id)
                except Exception as e:
                    result['errors'].append(f"重置机器ID失败: {str(e)}")

            # 2. 重置系统UUID
            try:
                new_uuid = str(uuid.uuid4()).upper()
                # 更新注册表中的机器GUID
                try:
                    key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                                       r"SOFTWARE\Microsoft\Cryptography",
                                       0, winreg.KEY_SET_VALUE)
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, new_uuid)
                    winreg.CloseKey(key)
                except Exception as e:
                    result['errors'].append(f"更新机器GUID失败: {str(e)}")
            except Exception as e:
                result['errors'].append(f"重置系统UUID失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"重置机器指纹失败: {str(e)}")

        return result

    def _modify_telemetry_data(self) -> Dict[str, Any]:
        """修改遥测数据"""
        result = {'success': True, 'errors': []}

        try:
            # 修改VSCode遥测设置
            settings_json_path = os.path.expandvars(r"%APPDATA%\Code\User\settings.json")

            if os.path.exists(settings_json_path):
                try:
                    with open(settings_json_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    # 禁用遥测
                    telemetry_settings = {
                        "telemetry.telemetryLevel": "off",
                        "telemetry.enableCrashReporter": False,
                        "telemetry.enableTelemetry": False
                    }

                    settings.update(telemetry_settings)

                    with open(settings_json_path, 'w', encoding='utf-8') as f:
                        json.dump(settings, f, indent=2, ensure_ascii=False)

                except Exception as e:
                    result['errors'].append(f"修改遥测设置失败: {str(e)}")

        except Exception as e:
            result['success'] = False
            result['errors'].append(f"修改遥测数据失败: {str(e)}")

        return result
    
    def _apply_anti_detection_measures(self) -> Dict[str, Any]:
        """应用反检测措施"""
        result = {
            'techniques': [],
            'errors': []
        }
        
        try:
            # 1. 虚拟机检测绕过
            if self.advanced_techniques['virtual_machine_detection_bypass']:
                vm_result = self._bypass_vm_detection()
                if vm_result['success']:
                    result['techniques'].append("虚拟机检测绕过")
            
            # 2. 浏览器指纹重置
            if self.advanced_techniques['browser_fingerprint_reset']:
                browser_result = self._reset_browser_fingerprints()
                if browser_result['success']:
                    result['techniques'].append("浏览器指纹重置")
            
            # 3. 系统时钟操作
            if self.advanced_techniques['system_clock_manipulation']:
                clock_result = self._manipulate_system_clock()
                if clock_result['success']:
                    result['techniques'].append("系统时钟操作")
            
        except Exception as e:
            result['errors'].append(f"应用反检测措施失败: {str(e)}")
        
        return result
